import React, { useState, useRef, useEffect } from 'react';
import { Input, Button, message } from 'antd';
import { SendOutlined, PaperClipOutlined } from '@ant-design/icons';
import styles from '../index.module.scss';

const { TextArea } = Input;

interface ChatInputProps {
  onSend: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSend,
  disabled = false,
  placeholder = '输入消息...',
  maxLength = 2000,
}) => {
  const [inputValue, setInputValue] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const textAreaRef = useRef<any>(null);

  // 自动调整文本框高度
  const adjustTextAreaHeight = () => {
    const textArea = textAreaRef.current?.resizableTextArea?.textArea;
    if (textArea) {
      textArea.style.height = 'auto';
      textArea.style.height = `${Math.min(textArea.scrollHeight, 120)}px`;
    }
  };

  useEffect(() => {
    adjustTextAreaHeight();
  }, [inputValue]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
  };

  const handleSend = () => {
    const trimmedValue = inputValue.trim();
    if (!trimmedValue) {
      message.warning('请输入消息内容');
      return;
    }

    if (trimmedValue.length > maxLength) {
      message.error(`消息长度不能超过 ${maxLength} 个字符`);
      return;
    }

    onSend(trimmedValue);
    setInputValue('');
    
    // 重置文本框高度
    setTimeout(() => {
      adjustTextAreaHeight();
    }, 0);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // 处理中文输入法
    if (isComposing) return;

    // Enter 发送，Shift+Enter 换行
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleCompositionStart = () => {
    setIsComposing(true);
  };

  const handleCompositionEnd = () => {
    setIsComposing(false);
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
    // 处理粘贴事件，可以在这里添加文件上传逻辑
    const items = e.clipboardData?.items;
    if (items) {
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.type.indexOf('image') !== -1) {
          // 处理图片粘贴
          e.preventDefault();
          message.info('暂不支持图片上传');
          break;
        }
      }
    }
  };

  const canSend = inputValue.trim().length > 0 && !disabled;

  return (
    <div className={styles.chatInput}>
      <div className={styles.inputContainer}>
        <div className={styles.inputWrapper}>
          <TextArea
            ref={textAreaRef}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onCompositionStart={handleCompositionStart}
            onCompositionEnd={handleCompositionEnd}
            onPaste={handlePaste}
            placeholder={placeholder}
            disabled={disabled}
            autoSize={{ minRows: 1, maxRows: 5 }}
            maxLength={maxLength}
            showCount={{
              formatter: ({ count, maxLength }) => 
                count > maxLength * 0.8 ? `${count}/${maxLength}` : ''
            }}
          />
        </div>
        
        <div className={styles.sendButton}>
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSend}
            disabled={!canSend}
            loading={disabled}
          >
            发送
          </Button>
        </div>
      </div>
      
      {/* 快捷键提示 */}
      <div style={{ 
        fontSize: '12px', 
        color: 'var(--text-tertiary)', 
        marginTop: '8px',
        textAlign: 'center'
      }}>
        按 Enter 发送，Shift + Enter 换行
      </div>
    </div>
  );
};

export default ChatInput;
