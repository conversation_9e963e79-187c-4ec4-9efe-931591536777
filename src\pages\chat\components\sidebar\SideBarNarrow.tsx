import React from "react";
import styles from "./sidebar.module.scss";
import { SvgIcon } from "@/components/SvgIcon";

export interface SideBarNarrowProps {
  className?: string;
}

export function SideBarNarrow({ className }: SideBarNarrowProps) {
  return (
    <div className={` ${styles["narrow-sidebar"]} ${className || ""}`}>
      <SvgIcon name="chatgpt" size={24} />
      <SvgIcon name="mask" size={16} />
      <SvgIcon name="chatgpt" size={24} /> <SvgIcon name="chatgpt" size={24} />{" "}
      <SvgIcon name="chatgpt" size={24} /> <SvgIcon name="chatgpt" size={24} />{" "}
      <SvgIcon name="chatgpt" size={24} />
      {/* 头部区域 - 窄模式 */}
    </div>
  );
}
