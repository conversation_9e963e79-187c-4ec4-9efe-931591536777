// 侧边栏主容器
.sidebar {
  width: var(--sidebar-width, 300px);
  height: 100vh;
  background-color: var(--second, #f7f7f7);
  border-right: 1px solid var(--border-color, #e0e0e0);
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 20px;
  box-sizing: border-box;
  transition: width 0.3s ease;


}



// 窄模式侧边栏
.narrow-sidebar {
  display: flex;
  width: 80px;

}

// 头部区域
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color, #e0e0e0);
}

.sidebar-header-narrow {
  .sidebar-title {
    font-size: 16px;
  }
  
  .sidebar-sub-title {
    display: none;
  }
}

.sidebar-title-container {
  flex: 1;
}

.sidebar-title {
  font-size: 20px;
  font-weight: bold;
  color: var(--black, #333);
  margin-bottom: 4px;
}

.sidebar-sub-title {
  font-size: 12px;
  color: var(--gray, #666);
  opacity: 0.7;
}

.sidebar-logo {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    width: 24px;
    height: 24px;
  }
}

// 功能按钮栏
.sidebar-header-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.sidebar-bar-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--white, #fff);
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: var(--black, #333);
  
  &:hover {
    background: var(--gray, #f5f5f5);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  svg {
    width: 16px;
    height: 16px;
  }
  
  span {
    white-space: nowrap;
  }
}

// 主体内容区域
.sidebar-body {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

// 聊天列表
.chat-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.chat-item {
  padding: 12px;
  background: var(--white, #fff);
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: var(--gray, #f5f5f5);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.chat-item-narrow {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  min-height: 40px;
}

.chat-item-avatar {
  font-size: 18px;
  text-align: center;
}

.chat-item-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--black, #333);
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-item-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--gray, #666);
}

.chat-item-count {
  opacity: 0.8;
}

.chat-item-date {
  opacity: 0.6;
}

// 底部操作区域
.sidebar-tail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid var(--border-color, #e0e0e0);
  margin-top: 20px;
}

.sidebar-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.sidebar-action-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--white, #fff);
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--gray, #f5f5f5);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.sidebar-new-chat-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--primary, #007bff);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;

  &:hover {
    background: var(--primary-dark, #0056b3);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

// 拖拽调整区域
.sidebar-drag {
  position: absolute;
  top: 0;
  right: -7px;
  width: 14px;
  height: 100%;
  background: var(--border-color, #e0e0e0);
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 7px;
  cursor: ew-resize;
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    opacity: 0.8;
  }

  svg {
    width: 8px;
    height: 16px;
    opacity: 0.6;
  }


}



// 深色模式支持
@media (prefers-color-scheme: dark) {
  .sidebar {
    background-color: var(--second-dark, #2d2d2d);
    border-right-color: var(--border-color-dark, #404040);
  }

  .sidebar-title {
    color: var(--white, #fff);
  }

  .sidebar-sub-title {
    color: var(--gray-dark, #ccc);
  }

  .sidebar-bar-button,
  .sidebar-action-button {
    background: var(--white-dark, #404040);
    border-color: var(--border-color-dark, #555);
    color: var(--white, #fff);

    &:hover {
      background: var(--gray-dark, #555);
    }
  }

  .chat-item {
    background: var(--white-dark, #404040);
    border-color: var(--border-color-dark, #555);

    &:hover {
      background: var(--gray-dark, #555);
    }
  }

  .chat-item-title {
    color: var(--white, #fff);
  }

  .chat-item-info {
    color: var(--gray-dark, #ccc);
  }
}
