import React, { useEffect, useState } from 'react';
import { Avatar, Divider, List, Skeleton } from 'antd';
import InfiniteScroll from 'react-infinite-scroll-component';
import styles from './index.module.scss';
interface DataType {
    gender?: string;
    name?: string;
    email?: string;
    avatar?: string;
    id?: string;
}

const HistoryList: React.FC = () => {
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState<DataType[]>([]);
    const [page, setPage] = useState(1);
    const [expandedIds, setExpandedIds] = useState<Set<string>>(new Set());

    const loadMoreData = () => {
        if (loading) return;
        setLoading(true);
        fetch(`https://660d2bd96ddfa2943b33731c.mockapi.io/api/users/?page=${page}&limit=10`)
            .then((res) => res.json())
            .then((res) => {
                const results = Array.isArray(res) ? res : [];
                setData(prev => [...prev, ...results]);
                setPage(prev => prev + 1);
            })
            .finally(() => setLoading(false));
    };

    useEffect(() => {
        loadMoreData();
    }, []);

    const toggleExpand = (id: string) => {
        setExpandedIds(prev => {
            const newSet = new Set(prev);
            if (newSet.has(id)) newSet.delete(id);
            else newSet.add(id);
            return newSet;
        });
    };

    return (
        <div id="scrollableDiv" className={styles.scrollableDiv}>
            <InfiniteScroll
                dataLength={data.length}
                next={loadMoreData}
                hasMore={data.length < 50}
                loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
                endMessage={<Divider plain>It is all, nothing more 🤐</Divider>}
                scrollableTarget="scrollableDiv"
            >
                <List
                    dataSource={data}
                    renderItem={(item) => {
                        const isExpanded = item.id && expandedIds.has(item.id);
                        return (
                            <List.Item
                                key={item.email}
                                onClick={() => item.id && toggleExpand(item.id)}
                                className={styles.expandableItem}
                            >
                                <List.Item.Meta
                                    avatar={<Avatar src={item.avatar} />}
                                    title={<span>{item.name}</span>}
                                    description={isExpanded ? item.email : null}
                                />
                                {isExpanded && <div className={styles.content}>Content</div>}
                            </List.Item>
                        );
                    }}
                />
            </InfiniteScroll>
        </div>
    );
};

export default HistoryList;
