import React, { useState } from "react";
import { Form, Input } from "antd";
import { UserOutlined, LockOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import Captcha from "@/components/Captcha";

const AccountLogin: React.FC = () => {
  const { t } = useTranslation();
  const [currentCaptcha, setCurrentCaptcha] = useState<string>("");

  // 验证码校验规则
  const validateCaptcha = (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error(t("login.captchaRequired")));
    }

    // 不区分大小写比较
    if (value.toLowerCase() !== currentCaptcha.toLowerCase()) {
      return Promise.reject(new Error(t("login.captchaError")));
    }

    return Promise.resolve();
  };

  return (
    <div className="form-item-group">
      <Form.Item
        name="username"
        rules={[
          { required: true, message: t("login.usernameRequired") },
          {
            pattern: /^[a-zA-Z0-9_-]{3,30}$|^1[3-9]\d{9}$/,
            message: "请输入正确的用户名或手机号",
          },
        ]}
      >
        <Input
          prefix={<UserOutlined />}
          placeholder={t("login.usernamePlaceholder")}
          className="login-input"
        />
      </Form.Item>
      <Form.Item
        name="password"
        rules={[{ required: true, message: t("login.passwordRequired") }]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder={t("login.passwordPlaceholder")}
          className="login-input"
        />
      </Form.Item>
      <div className="captcha-container">
        <Form.Item
          name="captcha"
          rules={[{ validator: validateCaptcha }]}
          className="captcha-input"
        >
          <Input
            prefix={<LockOutlined />}
            placeholder={t("login.captchaPlaceholder")}
            className="login-input"
          />
        </Form.Item>
        <Captcha
          onChange={(code) => {
            setCurrentCaptcha(code);
            console.log("验证码:", code);
          }}
        />
      </div>
    </div>
  );
};

export default AccountLogin;
