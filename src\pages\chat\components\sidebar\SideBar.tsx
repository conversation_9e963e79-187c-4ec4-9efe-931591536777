import React from "react";
import styles from "./sidebar.module.scss";
import { SvgIcon } from "@/components/SvgIcon";

export interface SideBarProps {
  className?: string;
}

export function SideBar({ className }: SideBarProps) {
  return (
    <div className={`${styles.sidebar} ${className || ""}`}>
      {/* 头部区域 */}
      <div className={styles["sidebar-header"]}>
        <div className={styles["sidebar-title-container"]}>
          <div className={styles["sidebar-title"]}>NextChat</div>
          <div className={styles["sidebar-sub-title"]}>
            Build your own AI assistant.
          </div>
        </div>
        <div className={styles["sidebar-logo"]}>
          <SvgIcon name="chatgpt" size={24} />
        </div>
      </div>

      {/* 功能按钮栏 */}
      <div className={styles["sidebar-header-bar"]}>
        <button className={styles["sidebar-bar-button"]}>
          <SvgIcon name="mask" size={16} />
          <span>面具</span>
        </button>
        <button className={styles["sidebar-bar-button"]}>
          <SvgIcon name="mcp" size={16} />
          <span>MCP</span>
        </button>
        <button className={styles["sidebar-bar-button"]}>
          <SvgIcon name="discovery" size={16} />
          <span>发现</span>
        </button>
      </div>

      {/* 主体内容区域 */}
      <div className={styles["sidebar-body"]}>
        <div className={styles["chat-list"]}>
          <div className={styles["chat-item"]}>
            <div className={styles["chat-item-title"]}>聊天记录 1</div>
            <div className={styles["chat-item-info"]}>
              <span className={styles["chat-item-count"]}>15 条对话</span>
              <span className={styles["chat-item-date"]}>今天</span>
            </div>
          </div>
          <div className={styles["chat-item"]}>
            <div className={styles["chat-item-title"]}>聊天记录 2</div>
            <div className={styles["chat-item-info"]}>
              <span className={styles["chat-item-count"]}>8 条对话</span>
              <span className={styles["chat-item-date"]}>昨天</span>
            </div>
          </div>
          <div className={styles["chat-item"]}>
            <div className={styles["chat-item-title"]}>聊天记录 3</div>
            <div className={styles["chat-item-info"]}>
              <span className={styles["chat-item-count"]}>23 条对话</span>
              <span className={styles["chat-item-date"]}>2天前</span>
            </div>
          </div>
        </div>
      </div>

      {/* 底部操作区域 */}
      <div className={styles["sidebar-tail"]}>
        {/* <div className={styles["sidebar-actions"]}>
          <button className={styles["sidebar-action-button"]}>
            <SvgIcon name="settings" size={16} />
          </button>
          <button className={styles["sidebar-action-button"]}>
            <SvgIcon name="github" size={16} />
          </button>
          <button className={styles["sidebar-action-button"]}>
            <SvgIcon name="delete" size={16} />
          </button>
        </div> */}
        <div className={styles["sidebar-actions"]}>
          <button className={styles["sidebar-new-chat-button"]}>
            <SvgIcon name="add" size={16} />
            <span>新的聊天</span>
          </button>
        </div>
      </div>

      {/* 拖拽调整区域 */}
      <div className={styles["sidebar-drag"]}>
        <SvgIcon name="drag" size={8} />
      </div>
    </div>
  );
}
