import React, { useState, useEffect, useRef } from "react";
import { Button, Typography, message, Spin } from "antd";
import { ClearOutlined, SettingOutlined } from "@ant-design/icons";
import ChatMessage, { Message } from "../components/ChatMessage";
import ChatInput from "../components/ChatInput";
import styles from "../index.module.scss";
import { SideBar } from "../components/sidebar/SideBar";
import { SideBarNarrow } from "../components/sidebar/SideBarNarrow";

const { Title, Text } = Typography;

const ChatPage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const chatBodyRef = useRef<HTMLDivElement>(null);

  // 滚动到底部
  const scrollToBottom = () => {
    if (chatBodyRef.current) {
      chatBodyRef.current.scrollTop = chatBodyRef.current.scrollHeight;
    }
  };

  // 当消息更新时滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 发送消息
  const handleSendMessage = async (content: string) => {
    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content,
      timestamp: Date.now(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setIsLoading(true);

    try {
      // 模拟AI回复
      await simulateAIResponse(content);
    } catch (error) {
      message.error("发送消息失败，请重试");
    } finally {
      setIsLoading(false);
    }
  };

  // 模拟AI回复
  const simulateAIResponse = async (userInput: string) => {
    // 创建流式回复消息
    const assistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      role: "assistant",
      content: "",
      timestamp: Date.now(),
      streaming: true,
    };

    setMessages((prev) => [...prev, assistantMessage]);

    // 模拟流式回复
    const responses = [
      "我理解您的问题。",
      "让我为您详细解答：",
      "\n\n这是一个很好的问题，涉及到多个方面：",
      "\n\n1. **技术层面**：需要考虑系统架构和性能优化",
      "\n2. **用户体验**：界面设计要简洁直观",
      "\n3. **安全性**：数据传输和存储的安全保障",
      "\n\n希望这个回答对您有帮助！如果您还有其他问题，请随时告诉我。",
    ];

    let currentContent = "";

    for (let i = 0; i < responses.length; i++) {
      await new Promise((resolve) =>
        setTimeout(resolve, 300 + Math.random() * 500)
      );
      currentContent += responses[i];

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === assistantMessage.id
            ? { ...msg, content: currentContent }
            : msg
        )
      );
    }

    // 完成流式回复
    setMessages((prev) =>
      prev.map((msg) =>
        msg.id === assistantMessage.id ? { ...msg, streaming: false } : msg
      )
    );
  };

  // 复制消息
  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    message.success("已复制到剪贴板");
  };

  // 编辑消息
  const handleEditMessage = (messageId: string) => {
    // TODO: 实现编辑功能
    message.info("编辑功能开发中...");
  };

  // 删除消息
  const handleDeleteMessage = (messageId: string) => {
    setMessages((prev) => prev.filter((msg) => msg.id !== messageId));
    message.success("消息已删除");
  };

  // 清空聊天记录
  const handleClearChat = () => {
    setMessages([]);
    message.success("聊天记录已清空");
  };

  return (
    <div className={styles.chat}>
      <div className={styles.sidebar}>
        <SideBar></SideBar>
      </div>

      <div className="flex flex-col w-full">
        {/* 聊天头部 */}
        <div className={styles.chatHeader}>
          <div>
            <Title level={4} className={styles.headerTitle}>
              AI 助手
            </Title>
          </div>
          <div className={styles.headerActions}>
            <Button
              type="text"
              icon={<ClearOutlined />}
              onClick={handleClearChat}
              disabled={messages.length === 0}
            >
              清空
            </Button>
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => message.info("设置功能开发中...")}
            >
              设置
            </Button>
          </div>
        </div>

        {/* 聊天消息区域 */}
        <div className={styles.chatBody} ref={chatBodyRef}>
          <div className={styles.messageContainer}>
            {messages.length === 0 ? (
              <div className={styles.welcomeMessage}>
                <div className={styles.welcomeTitle}>欢迎使用 AI 助手</div>
                <div className={styles.welcomeSubtitle}>
                  我是您的智能助手，可以帮助您解答问题、提供建议和协助完成各种任务
                </div>
                <div className={styles.welcomeActions}>
                  <Button
                    type="primary"
                    onClick={() => handleSendMessage("你好，请介绍一下你自己")}
                  >
                    开始对话
                  </Button>
                  <Button onClick={() => handleSendMessage("你能帮我做什么？")}>
                    了解功能
                  </Button>
                </div>
              </div>
            ) : (
              messages.map((message) => (
                <ChatMessage
                  key={message.id}
                  message={message}
                  onCopy={handleCopyMessage}
                  onEdit={handleEditMessage}
                  onDelete={handleDeleteMessage}
                />
              ))
            )}

            {/* 加载状态 */}
            {isLoading && (
              <div className={styles.loadingMessage}>
                <Spin size="small" />
                <span style={{ marginLeft: 8 }}>AI 正在思考中...</span>
              </div>
            )}
          </div>
        </div>
        <SideBarNarrow></SideBarNarrow>
        {/* 聊天输入区域 */}
        <ChatInput
          onSend={handleSendMessage}
          disabled={isLoading}
          placeholder="输入您的问题..."
        />
      </div>
    </div>
  );
};

export default ChatPage;

export const routeConfig = {
  title: "route.chat.chat",
  sort: 5,
  layout: false, // 设置为独立页面，不在主布局中显示
};
