# 修改总结

## 需求

要求 mock 数据在 index 中生成的 timeline 组件的时间线数据结构以 attack 图谱组件中的数据结构为准，并且随着时间线的自动更新，图谱也对应更新节点，图谱中的原始数据可以注释掉。

## 修改内容

### 1. 修改 `src/pages/superagent/mockData/mock.tsx`

- **数据结构重构**: 将原有的简单事件数据结构改为包含图谱信息的结构
- **新增字段**:
  - `timestamp`: 替代原来的 `label` 字段
  - `nodes`: 包含该事件相关的图谱节点数组
  - `edges`: 包含该事件相关的图谱边数组
- **新增辅助函数**:
  - `getNodeType()`: 根据内容自动判断节点类型
  - `getNodeStyle()`: 获取节点样式配置
  - `createNode()`: 创建节点对象
  - `createEdge()`: 创建边对象
- **数据转换**: 将 15 个安全事件转换为新的数据结构，每个事件包含相应的节点和边信息

### 2. 修改 `src/pages/superagent/graph/index.tsx`

- **接口更新**: 更新 `SecurityEvent` 接口以匹配新的数据结构
- **新增接口**: 添加 `GraphData` 接口定义图谱数据结构
- **状态管理**: 添加 `graphData` 状态来累积图谱数据
- **数据处理**: 修改 `processSecurityEvents` 函数使用 `timestamp` 字段
- **新增函数**: `updateGraphData()` 用于累积图谱数据，避免重复节点和边
- **定时器逻辑**: 修改定时器推送逻辑，同时更新 timeline 和图谱数据
- **组件传参**: 将累积的图谱数据传递给 `AttackGraph` 组件

### 3. 修改 `src/pages/superagent/components/attack.tsx`

- **原始数据注释**: 将静态的图谱数据注释掉
- **接口定义**: 添加 `GraphData` 和 `AttackGraphProps` 接口
- **组件重构**: 修改组件为接收 `graphData` props
- **状态管理**: 移除静态数据的状态管理，使用传入的动态数据
- **响应式更新**: 添加 `useEffect` 监听 `graphData` 变化并更新图谱
- **节点计数**: 根据传入的图谱数据动态更新节点计数

## 实现效果

1. **数据驱动**: Timeline 组件的数据结构现在完全基于图谱组件的数据结构
2. **同步更新**: 当 timeline 显示新事件时，图谱会同步添加相应的节点和边
3. **避免重复**: 实现了节点和边的去重逻辑，避免重复添加相同的图谱元素
4. **动态渲染**: 图谱会根据 timeline 的进度动态构建攻击路径
5. **保持功能**: 保留了原有的 timeline 显示格式和图谱的交互功能

## 数据流

```
Mock数据 -> Index.tsx (Timeline + 累积图谱数据) -> AttackGraph组件
```

每当 timeline 推送新事件时：

1. 更新 timeline 显示
2. 提取事件中的 nodes 和 edges
3. 去重后添加到累积的图谱数据中
4. 传递给 AttackGraph 组件进行渲染

## 技术特点

- 类型安全的 TypeScript 接口定义
- 响应式的数据更新机制
- 高效的去重算法
- 保持原有 UI 和交互体验

## 后续优化 - markerSize 统一

### 修改内容

1. **统一 markerSize 为 14**: 将所有边的标记大小统一设置为 14，简化代码逻辑
2. **简化 createEdge 函数**: 移除了 markerSize 的条件判断，只保留颜色的区分
3. **简化随机边生成**: 移除了随机 markerSize 的逻辑
4. **更新默认值**: 将 FlyMarkerCubic 类中的默认 markerSize 改为 14
5. **清理注释**: 移除了不再需要的动态 markerSize 相关注释

### 简化效果

- 代码更加简洁，减少了不必要的条件判断
- 视觉效果更加统一，所有连接线的标记大小一致
- 维护成本降低，减少了配置复杂度
