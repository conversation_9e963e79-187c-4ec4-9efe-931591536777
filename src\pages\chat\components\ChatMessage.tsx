import React from "react";
import { <PERSON><PERSON>, Tooltip } from "antd";
import {
  CopyOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  RobotOutlined,
} from "@ant-design/icons";
import MarkdownRenderer from "./MarkdownRenderer";
import styles from "../index.module.scss";

export interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: number;
  streaming?: boolean;
}

interface ChatMessageProps {
  message: Message;
  onCopy?: (content: string) => void;
  onEdit?: (messageId: string) => void;
  onDelete?: (messageId: string) => void;
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  onCopy,
  onEdit,
  onDelete,
}) => {
  const isUser = message.role === "user";

  const handleCopy = () => {
    if (onCopy) {
      onCopy(message.content);
    } else {
      navigator.clipboard.writeText(message.content);
    }
  };

  const handleEdit = () => {
    onEdit?.(message.id);
  };

  const handleDelete = () => {
    onDelete?.(message.id);
  };

  return (
    <div
      className={`${styles.message} ${
        isUser ? styles.userMessage : styles.assistantMessage
      }`}
    >
      <div className={styles.messageAvatar}>
        {isUser ? <UserOutlined /> : <RobotOutlined />}
      </div>

      <div className={styles.messageContent}>
        <MarkdownRenderer content={message.content} />

        {message.streaming && (
          <div className={styles.loadingMessage}>
            <span>正在输入</span>
            <div className={styles.loadingDots}>
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        )}

        <div className={styles.messageActions}>
          <Tooltip title="复制">
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              onClick={handleCopy}
            />
          </Tooltip>

          {isUser && (
            <Tooltip title="编辑">
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                onClick={handleEdit}
              />
            </Tooltip>
          )}

          <Tooltip title="删除">
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              onClick={handleDelete}
              danger
            />
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
