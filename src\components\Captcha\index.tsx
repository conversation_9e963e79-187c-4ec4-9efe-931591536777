import React, { useEffect, useState } from "react";
import { getUUID } from "@/utils/captcha";
import { request } from "@/utils/request";

interface CaptchaProps {
  width?: number;
  height?: number;
  onChange?: (uuid: string) => void;
}

const Captcha: React.FC<CaptchaProps> = ({
  width = 100,
  height = 40,
  onChange,
}) => {
  const [captchaUrl, setCaptchaUrl] = useState("");

  const refreshCaptcha = async () => {
    try {
      const newUuid = getUUID();
      const response = (await request.get(`/captcha.jpg?uuid=${newUuid}`, {
        responseType: "blob",
      })) as Blob;

      const imageUrl = URL.createObjectURL(response);
      setCaptchaUrl(imageUrl);
      onChange?.(newUuid);
    } catch (error) {
      console.error("获取验证码失败:", error);
    }
  };

  useEffect(() => {
    refreshCaptcha();

    // 清理函数，释放blob URL
    return () => {
      if (captchaUrl) {
        URL.revokeObjectURL(captchaUrl);
      }
    };
  }, []);

  return (
    <div
      className="captcha-image-container flex-shrink-0 cursor-pointer"
      onClick={refreshCaptcha}
      title="点击刷新验证码"
    >
      <img
        src={captchaUrl}
        alt="验证码"
        width={width}
        height={height}
        className="h-full w-full object-cover"
      />
    </div>
  );
};

export default Captcha;
