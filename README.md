<p align="center">
  <img  style='width: 150px;' src="https://bestycw.github.io/img-resource.github.io/img/logo.png" alt="React 18">
 
</p>
<div align="center">

[简体中文](./README.md) | [English](./README.en-US.md)

</div>
<h1 align="center" style="margin: 30px 0 30px; font-weight: bold;">React Admin Antd 1.0</h1>
<h4 align="center">基于React 18、Ant Design 5.0、TypeScript、Vite 的现代化后台管理系统模板。</h4>
<p align="center">
  在咖啡馆创作，所以起名Coffee。
</p>

## 🚀 关于平台
   一套完全开源的快速开发平台，支持个人和企业使用
   * 主要技术框架是React 18、Ant Design 5.0、TypeScript、Vite 4、MobX、TailwindCSS
   * 支持多种主题风格、主题色、布局方式
   * 支持国际化、权限管理、动态路由、按钮级权限控制
   * 支持响应式设计，在不同尺寸下自动调整
   * 约定式路由和菜单生成
   * 封装完善的Axios和Fetch，同时支持websocket和流式传输等
   * 内置多种编辑器
## 在线预览
  admin/123456
  [在线预览](https://bestycw.github.io/react-admin-antd/)
## 📝 文档
  [在线文档](https://bestycw.github.io/coffee-react-admin-docs/) 
  还比较简陋，逐步完善。
## 🚀 主要功能

<table>
    <tr>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E7%99%BB%E5%BD%95%E9%A1%B5.png"/></td>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E9%A6%96%E9%A1%B5.png"/></td>
    </tr>
    <tr>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E8%AF%B7%E6%B1%82%E5%8A%9F%E8%83%BD.png"/></td>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E9%A6%96%E9%A1%B5-%E7%BB%8F%E5%85%B8%E9%A3%8E%E6%A0%BC.png"/></td>
    </tr>
    <tr>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E8%A1%A8%E5%8D%95%E5%8A%9F%E8%83%BD.png"/></td>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E8%A1%A8%E6%A0%BC%E5%8A%9F%E8%83%BD.png"/></td>
    </tr>
	<tr>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E7%BC%96%E8%BE%91%E5%99%A8.png"/></td>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E7%BB%84%E4%BB%B6%E5%8A%9F%E8%83%BD.png"/></td>
    </tr>	 
    <tr>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E5%B0%8F%E5%B1%8F%E5%B9%95.png"/></td>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E7%B3%BB%E7%BB%9F%E9%85%8D%E7%BD%AE.png"/></td>
    </tr>

</table>

## ❌后期TODO
  * ICON的处理
  * 丰富组件库：流程图、地图、三维相关、日历图等
  * 脚手架搭建
  * 支持约定式路由的更丰富配置
  * 基于Arco Design的版本
  * Electron版本
  * 基于shadcn-ui的版本


## 🤝 贡献

欢迎提交 Issue 和 Pull Request，感谢您的贡献！同时欢迎 star 和 fork。后续会更加完善！

## 如何贡献

非常欢迎您的加入！[提一个 Issue](https://github.com/bestycw/react-admin-antd/issues) 或者提交一个 `Pull Request` 前期可能bug较多，多担待！

**Pull Request:**

1. Fork 代码!
2. 创建自己的分支: `git checkout -b feat/xxxx`
3. 提交您的修改: `git commit -am 'feat(function): add xxxxx'`
4. 推送您的分支: `git push origin feat/xxxx`
5. 提交`pull request`




## 浏览器支持

本地开发推荐使用 `Chrome`、`Edge`、`Firefox` 浏览器，作者常用的是最新版 `Chrome` 浏览器  
实际使用中感觉 `Firefox` 在动画上要比别的浏览器更加丝滑，只是作者用 `Chrome` 已经习惯了，看个人爱好选择吧  

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt=" Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>IE | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt=" Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Edge | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Firefox | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Chrome | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Safari |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: | :-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: | :-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: |
|                                                                                                不支持                                                                                                |                                                                                              最后两个版本                                                                                              |                                                                                                   最后两个版本                                                                                                    |                                                                                                 最后两个版本                                                                                                  |                                                                                                 最后两个版本                                                                                                  |
## 许可证

完全免费开源


## `Star`

非常感谢留下星星的好心人，感谢您的支持 :heart:
## 

## 如有合作需求，可以联系 发邮件********************* 