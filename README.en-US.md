<p align="center">
  <img  style='width: 150px;' src="https://bestycw.github.io/img-resource.github.io/img/logo.png" alt="React 18">
</p>

<div align="center">

[简体中文](./README.md) | [English](./README.en-US.md)

</div>

<h1 align="center" style="margin: 30px 0 30px; font-weight: bold;">React Admin Antd 1.0</h1>
<h4 align="center">A modern admin template based on React 18, Ant Design 5.0, TypeScript, and Vite.</h4>
<p align="center">
  Created in a coffee shop, hence named Coffee.
</p>

## 🚀 About
   A completely open-source rapid development platform, suitable for both personal and enterprise use
   * Main tech stack: React 18, Ant Design 5.0, TypeScript, Vite 4, MobX, TailwindCSS
   * Multiple theme styles, theme colors, and layout options
   * Supports internationalization, permission management, dynamic routing, and button-level permission control
   * Responsive design that automatically adjusts to different screen sizes
   * Convention-based routing and menu generation
   * Well-encapsulated Axios and Fetch, with support for websocket and streaming
   * Built-in multiple editors

## Online Preview
  admin/123456
  [Live Demo](https://bestycw.github.io/react-admin-antd/)

## 📝 Documentation
  [Online Documentation](https://bestycw.github.io/coffee-react-admin-docs/)
  Still basic, being improved gradually.

## 🚀 Key Features

<table>
    <tr>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E7%99%BB%E5%BD%95%E9%A1%B5.png"/></td>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E9%A6%96%E9%A1%B5.png"/></td>
    </tr>
    <tr>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E8%AF%B7%E6%B1%82%E5%8A%9F%E8%83%BD.png"/></td>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E9%A6%96%E9%A1%B5-%E7%BB%8F%E5%85%B8%E9%A3%8E%E6%A0%BC.png"/></td>
    </tr>
    <tr>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E8%A1%A8%E5%8D%95%E5%8A%9F%E8%83%BD.png"/></td>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E8%A1%A8%E6%A0%BC%E5%8A%9F%E8%83%BD.png"/></td>
    </tr>
    <tr>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E7%BC%96%E8%BE%91%E5%99%A8.png"/></td>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E7%BB%84%E4%BB%B6%E5%8A%9F%E8%83%BD.png"/></td>
    </tr>	 
    <tr>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E5%B0%8F%E5%B1%8F%E5%B9%95.png"/></td>
        <td><img src="https://bestycw.github.io/img-resource.github.io/img/%E7%B3%BB%E7%BB%9F%E9%85%8D%E7%BD%AE.png"/></td>
    </tr>
</table>

## ❌ Future TODOs
  * Icon handling improvements
  * Enrich component library: flowcharts, maps, 3D-related components, calendar charts, etc.
  * Scaffold construction
  * Support richer configuration for convention-based routing
  * Version based on Arco Design
  * Electron version
  * Version based on shadcn-ui

## 🤝 Contributing

Issues and Pull Requests are welcome! Thanks for your contribution! Also welcome to star and fork. More improvements coming soon!

## How to Contribute

Your participation is highly welcome! [Submit an Issue](https://github.com/bestycw/react-admin-antd/issues) or submit a `Pull Request`. There might be some bugs in the early stages, please be patient!

**Pull Request:**

1. Fork the code!
2. Create your feature branch: `git checkout -b feat/xxxx`
3. Commit your changes: `git commit -am 'feat(function): add xxxxx'`
4. Push to your branch: `git push origin feat/xxxx`
5. Submit a `pull request`

## Browser Support

For local development, we recommend using `Chrome`, `Edge`, or `Firefox` browsers. The author typically uses the latest version of `Chrome`.
From experience, `Firefox` seems to have smoother animations compared to other browsers, but it's a matter of personal preference.

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt=" Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>IE | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt=" Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Edge | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Firefox | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Chrome | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Safari |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: | :-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: | :-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: |
|                                                                                             Not Supported                                                                                              |                                                                                          Last 2 Versions                                                                                           |                                                                                               Last 2 Versions                                                                                                |                                                                                             Last 2 Versions                                                                                              |                                                                                             Last 2 Versions                                                                                              |

## License

Completely free and open source

## `Star`

Many thanks to those who have left a star, thank you for your support :heart:

## Contact

For business cooperation, please contact via email: <EMAIL> 