import React from 'react';
import { Typography } from 'antd';

const { Text, Paragraph } = Typography;

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, className }) => {
  // 简单的 Markdown 解析器
  const parseMarkdown = (text: string) => {
    const lines = text.split('\n');
    const elements: React.ReactNode[] = [];
    let currentParagraph: string[] = [];
    let inCodeBlock = false;
    let codeBlockContent: string[] = [];
    let codeBlockLanguage = '';

    const flushParagraph = () => {
      if (currentParagraph.length > 0) {
        const paragraphText = currentParagraph.join('\n');
        elements.push(
          <Paragraph key={elements.length} style={{ marginBottom: 8 }}>
            {parseInlineElements(paragraphText)}
          </Paragraph>
        );
        currentParagraph = [];
      }
    };

    const parseInlineElements = (text: string): React.ReactNode[] => {
      const parts: React.ReactNode[] = [];
      let remaining = text;
      let key = 0;

      while (remaining.length > 0) {
        // 处理行内代码
        const codeMatch = remaining.match(/^`([^`]+)`/);
        if (codeMatch) {
          parts.push(
            <Text key={key++} code style={{ fontSize: '13px' }}>
              {codeMatch[1]}
            </Text>
          );
          remaining = remaining.slice(codeMatch[0].length);
          continue;
        }

        // 处理粗体
        const boldMatch = remaining.match(/^\*\*([^*]+)\*\*/);
        if (boldMatch) {
          parts.push(
            <Text key={key++} strong>
              {boldMatch[1]}
            </Text>
          );
          remaining = remaining.slice(boldMatch[0].length);
          continue;
        }

        // 处理斜体
        const italicMatch = remaining.match(/^\*([^*]+)\*/);
        if (italicMatch) {
          parts.push(
            <Text key={key++} italic>
              {italicMatch[1]}
            </Text>
          );
          remaining = remaining.slice(italicMatch[0].length);
          continue;
        }

        // 处理普通文本
        const nextSpecialChar = remaining.search(/[`*]/);
        if (nextSpecialChar === -1) {
          parts.push(remaining);
          break;
        } else {
          parts.push(remaining.slice(0, nextSpecialChar));
          remaining = remaining.slice(nextSpecialChar);
        }
      }

      return parts;
    };

    lines.forEach((line, index) => {
      // 处理代码块
      if (line.startsWith('```')) {
        if (!inCodeBlock) {
          flushParagraph();
          inCodeBlock = true;
          codeBlockLanguage = line.slice(3).trim();
          codeBlockContent = [];
        } else {
          inCodeBlock = false;
          elements.push(
            <div
              key={elements.length}
              style={{
                background: 'var(--bg-tertiary)',
                padding: '12px',
                borderRadius: 'var(--radius)',
                margin: '8px 0',
                overflow: 'auto',
                fontSize: '13px',
                fontFamily: 'Monaco, Menlo, Ubuntu Mono, monospace',
                border: '1px solid var(--border-primary)',
              }}
            >
              <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                <code>{codeBlockContent.join('\n')}</code>
              </pre>
            </div>
          );
          codeBlockContent = [];
          codeBlockLanguage = '';
        }
        return;
      }

      if (inCodeBlock) {
        codeBlockContent.push(line);
        return;
      }

      // 处理标题
      if (line.startsWith('# ')) {
        flushParagraph();
        elements.push(
          <Typography.Title key={elements.length} level={1} style={{ marginBottom: 16 }}>
            {line.slice(2)}
          </Typography.Title>
        );
        return;
      }

      if (line.startsWith('## ')) {
        flushParagraph();
        elements.push(
          <Typography.Title key={elements.length} level={2} style={{ marginBottom: 12 }}>
            {line.slice(3)}
          </Typography.Title>
        );
        return;
      }

      if (line.startsWith('### ')) {
        flushParagraph();
        elements.push(
          <Typography.Title key={elements.length} level={3} style={{ marginBottom: 8 }}>
            {line.slice(4)}
          </Typography.Title>
        );
        return;
      }

      // 处理列表
      if (line.match(/^\d+\.\s/) || line.startsWith('- ') || line.startsWith('* ')) {
        flushParagraph();
        const listContent = line.replace(/^(\d+\.\s|[-*]\s)/, '');
        elements.push(
          <div key={elements.length} style={{ marginLeft: 16, marginBottom: 4 }}>
            <Text>• {parseInlineElements(listContent)}</Text>
          </div>
        );
        return;
      }

      // 处理引用
      if (line.startsWith('> ')) {
        flushParagraph();
        elements.push(
          <div
            key={elements.length}
            style={{
              borderLeft: '3px solid var(--primary-color)',
              paddingLeft: 12,
              margin: '8px 0',
              color: 'var(--text-secondary)',
              fontStyle: 'italic',
            }}
          >
            {parseInlineElements(line.slice(2))}
          </div>
        );
        return;
      }

      // 处理空行
      if (line.trim() === '') {
        flushParagraph();
        return;
      }

      // 普通段落
      currentParagraph.push(line);
    });

    // 处理最后的段落
    flushParagraph();

    return elements;
  };

  return (
    <div className={className} style={{ lineHeight: 1.6 }}>
      {parseMarkdown(content)}
    </div>
  );
};

export default MarkdownRenderer;
